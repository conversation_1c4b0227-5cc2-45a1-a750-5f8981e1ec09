import { Rect, Text, Group } from 'react-konva';

const SCALE = 1; // Escala 1:500 → 1m = 1px
const WIDTH_M = 12; // Ancho de la vivienda
const HEIGHT_M = 10; // Alto de la vivienda

const drawBox = (
  x: number,
  y: number,
  w: number,
  h: number,
  label: string,
  color: string = '#ddd'
) => (
  <Group>
    <Rect x={x} y={y} width={w} height={h} fill={color} stroke="black" />
    <Text text={label} x={x + 5} y={y + 5} fontSize={10} fill="#000" />
  </Group>
);

const FloorPlan3 = () => {
  const m2px = (m: number): number => m * SCALE * 10;

  // Coordenadas base para la vivienda (120 m²)
  const x0 = 10;
  const y0 = 10;

  // Vivienda tercera planta 120 m² (12m x 10m)
  const vivienda = drawBox(x0, y0, m2px(WIDTH_M), m2px(HEIGHT_M), 'Tercera Planta 120 m²', '#D5E8D4');

  return (
    <Group>
      <Text text="Tercera Planta (P3)" x={0} y={0} fontSize={14} fill="#000" fontStyle="bold" />
      {vivienda}
    </Group>
  );
};

export default FloorPlan3;
