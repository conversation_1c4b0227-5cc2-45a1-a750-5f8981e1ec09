import { Stage, Layer, Rect, Text, Group } from 'react-konva';

const SCALE = 2; // Escala 1:500 → 1m = 2px
const WIDTH_M = 19;
const HEIGHT_M = 25;
const WIDTH_PX = WIDTH_M * SCALE * 10;
const HEIGHT_PX = HEIGHT_M * SCALE * 10;

const drawBox = (
  x: number,
  y: number,
  w: number,
  h: number,
  label: string,
  color: string = '#ddd'
) => (
  <Group>
    <Rect x={x} y={y} width={w} height={h} fill={color} stroke="black" />
    <Text text={label} x={x + 5} y={y + 5} fontSize={10} fill="#000" />
  </Group>
);

const FloorPlanPB = () => {
  const m2px = (m: number): number => m * SCALE * 10;

  // Coordenadas base
  const x0 = 10;
  let y = 10;

  const portal = drawBox(x0, y, m2px(6), m2px(2.5), 'Portal + Conserje', '#F4CCCC');
  const vivienda = drawBox(x0 + m2px(6), y, m2px(12), m2px(10), 'Vivienda 105 m²', '#CFE2F3');

  y += m2px(10.5);
  const rampaGaraje = drawBox(x0 + m2px(14), y, m2px(3.2), m2px(12.5), 'Rampa Garaje', '#EAD1DC');
  const residuos = drawBox(x0, y, m2px(2), m2px(3), 'Residuos', '#D9EAD3');
  const tecnico = drawBox(x0 + m2px(2), y, m2px(2), m2px(4), 'Cuarto Técnico', '#E6B8AF');

  y += m2px(4.2);
  const rampaPeatonal = drawBox(x0 + m2px(4), y, m2px(1.5), m2px(8), 'Rampa Acceso', '#FFF2CC');
  const jardin = drawBox(x0 + m2px(6), y, m2px(10), m2px(14), 'Zona ajardinada', '#D0E0E3');

  y += m2px(14.2);
  const piscina = drawBox(x0, y, m2px(15), m2px(4), 'Piscina', '#9FC5E8');
  const solarium = drawBox(x0, y + m2px(4.2), m2px(10), m2px(5), 'Solárium', '#FCE5CD');

  return (
    <Stage width={WIDTH_PX + 40} height={HEIGHT_PX + 40}>
      <Layer>
        <Text text="Plano Planta Baja - Escala 1:500" x={10} y={5} fontSize={16} fill="#000" />
        <Rect x={10} y={10} width={WIDTH_PX} height={HEIGHT_PX} stroke="red" dash={[4, 2]} />
        {portal}
        {vivienda}
        {rampaGaraje}
        {residuos}
        {tecnico}
        {rampaPeatonal}
        {jardin}
        {piscina}
        {solarium}
      </Layer>
    </Stage>
  );
};

export default FloorPlanPB;
