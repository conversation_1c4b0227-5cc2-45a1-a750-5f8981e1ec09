import { Rect, Text, Group } from 'react-konva';

const SCALE = 1; // Escala 1:500 → 1m = 1px
const PARCELA_WIDTH_M = 22; // Ancho total de la parcela
const PARCELA_HEIGHT_M = 22; // Alto total de la parcela (485 m² ≈ 22x22)

const drawBox = (
  x: number,
  y: number,
  w: number,
  h: number,
  label: string,
  area: string,
  color: string = '#ddd'
) => (
  <Group>
    <Rect x={x} y={y} width={w} height={h} fill={color} stroke="black" strokeWidth={1} />
    <Text text={label} x={x + 2} y={y + 2} fontSize={8} fill="#000" fontStyle="bold" />
    <Text text={area} x={x + 2} y={y + 12} fontSize={7} fill="#333" />
  </Group>
);

const ParcelaView = () => {
  const m2px = (m: number): number => m * SCALE * 10;

  // Coordenadas base
  const x0 = 10;
  const y0 = 30;

  // Elementos de la parcela según especificaciones
  
  // Casa principal (120 m²) - centro de la parcela
  const casa = drawBox(
    x0 + m2px(6), 
    y0 + m2px(6), 
    m2px(12), 
    m2px(10), 
    '🏠 Casa', 
    '120 m²', 
    '#CFE2F3'
  );

  // Rampa de garaje (40 m²) - lateral
  const rampaGaraje = drawBox(
    x0 + m2px(18), 
    y0 + m2px(2), 
    m2px(3.2), 
    m2px(12.5), 
    '🛣️ Rampa Garaje', 
    '40 m²', 
    '#EAD1DC'
  );

  // Área de residuos (6 m²) - cerca del acceso
  const residuos = drawBox(
    x0, 
    y0, 
    m2px(2), 
    m2px(3), 
    '🗑️ Residuos', 
    '6 m²', 
    '#D9EAD3'
  );

  // Cuarto técnico (8 m²)
  const tecnico = drawBox(
    x0 + m2px(2), 
    y0, 
    m2px(2), 
    m2px(4), 
    '🔌 C. Técnico', 
    '8 m²', 
    '#E6B8AF'
  );

  // Rampa peatonal (12 m²)
  const rampaPeatonal = drawBox(
    x0 + m2px(4), 
    y0, 
    m2px(1.5), 
    m2px(8), 
    '🧑‍🦽 Rampa', 
    '12 m²', 
    '#FFF2CC'
  );

  // Zona ajardinada (140 m²)
  const jardin = drawBox(
    x0, 
    y0 + m2px(8), 
    m2px(10), 
    m2px(14), 
    '🪴 Jardín', 
    '140 m²', 
    '#D0E0E3'
  );

  // Piscina (60 m²)
  const piscina = drawBox(
    x0 + m2px(10), 
    y0 + m2px(16), 
    m2px(15), 
    m2px(4), 
    '🏊 Piscina', 
    '60 m²', 
    '#9FC5E8'
  );

  // Solárium/Terraza (50 m²)
  const solarium = drawBox(
    x0 + m2px(10), 
    y0 + m2px(20), 
    m2px(10), 
    m2px(5), 
    '🧘 Solárium', 
    '50 m²', 
    '#FCE5CD'
  );

  return (
    <Group>
      <Text text="Parcela - Escala 1:500" x={0} y={0} fontSize={14} fill="#000" fontStyle="bold" />
      <Text text="Superficie total: 485 m²" x={0} y={15} fontSize={12} fill="#666" />
      
      {/* Perímetro de la parcela */}
      <Rect 
        x={x0} 
        y={y0} 
        width={m2px(PARCELA_WIDTH_M)} 
        height={m2px(PARCELA_HEIGHT_M)} 
        stroke="red" 
        strokeWidth={2}
        dash={[5, 3]} 
        fill="transparent"
      />
      
      {/* Elementos de la parcela */}
      {casa}
      {rampaGaraje}
      {residuos}
      {tecnico}
      {rampaPeatonal}
      {jardin}
      {piscina}
      {solarium}
      
      {/* Área de circulación (calculada automáticamente como espacio restante) */}
      <Text 
        text="🚶 Circulación/Accesos: 49 m²" 
        x={x0} 
        y={y0 + m2px(PARCELA_HEIGHT_M) + 10} 
        fontSize={10} 
        fill="#666" 
      />
    </Group>
  );
};

export default ParcelaView;
