import { useState } from "react";
import { Stage, Layer } from "react-konva";
import ParcelaView from "./components/ParcelaView";
import FloorPlanPB from "./components/FloorPlanPB";
import FloorPlan1 from "./components/FloorPlan1";
import FloorPlan2 from "./components/FloorPlan2";
import FloorPlan3 from "./components/FloorPlan3";

const App = () => {
  const [selectedFloor, setSelectedFloor] = useState("Parcela");
  const floors = ["Parcela", "PB", "P1", "P2", "P3"];

  return (
    <div style={{ padding: "20px" }}>
      <div>
        <h2>Datos del catastro</h2>
        <ul>
          <li>📐 Escala catastral: 1/500</li>
          <li>📍 Ubicación: Calle Cantueso 67, 28029 Madrid</li>
          <li>📏 Superficie gráfica total: 485 m²</li>
          <li>🏗️ Terreno calificado como suelo urbano sin edificar</li>
          <li>Altura (12 m) permitida: PB + 3 plantas → 4 niveles</li>
          <li>➗ 485 m² (parcela) – 120 m² (PB ocupada) = 365 m² libres</li>
        </ul>
      </div>

      {/* <div>
        <h2>Planta baja</h2>
        <table style={{ width: "100%", borderCollapse: "collapse" }} border={1}>
          <tr>
            <th>Elemento</th>
            <th>m²</th>
            <th>Dimensiones (Ancho × Largo)</th>
            <th>Comentario</th>
          </tr>
          <tr>
            <td>🛣️ Rampa de garaje</td>
            <td>40 m²</td>
            <td>3.2 m × 12.5 m</td>
            <td>Inclinación ~15%, puede ir al lateral</td>
          </tr>
          <tr>
            <td>🗑️ Área de residuos</td>
            <td>6 m²</td>
            <td>2 m × 3 m</td>
            <td>Ideal cerca del acceso peatonal</td>
          </tr>
          <tr>
            <td>🔌 Cuarto técnico</td>
            <td>8 m²</td>
            <td>2 m × 4 m</td>
            <td>Contadores, telecomunicaciones, etc.</td>
          </tr>
          <tr>
            <td>🧑‍🦽 Rampa peatonal</td>
            <td>12 m²</td>
            <td>1.5 m × 8 m</td>
            <td>Con descansillos y barandilla</td>
          </tr>
          <tr>
            <td>🪴 Zona ajardinada</td>
            <td>140 m²</td>
            <td>10 m × 14 m</td>
            <td>Césped, caminos, sombra, bancos</td>
          </tr>
          <tr>
            <td>🏊 Piscina</td>
            <td>60 m²</td>
            <td>4 m × 15 m</td>
            <td>Estilo estrecho y largo, ideal para uso comunitario</td>
          </tr>
          <tr>
            <td>🧘 Solárium / Terraza</td>
            <td>50 m²</td>
            <td>5 m × 10 m</td>
            <td>Junto a la piscina, con tumbonas o mesa</td>
          </tr>
          <tr>
            <td>🚶 Circulación/Accesos</td>
            <td>39 m²</td>
            <td>variable (bordes, caminos)</td>
            <td>Zonas de paso entre secciones</td>
          </tr>
        </table>
      </div> */}

      <h2>Plano de planta</h2>
      <div style={{ marginBottom: "10px" }}>
        {floors.map((floor) => (
          <button
            key={floor}
            onClick={() => setSelectedFloor(floor)}
            style={{
              marginRight: "10px",
              padding: "8px 16px",
              backgroundColor: selectedFloor === floor ? "#ccc" : "#eee",
              border: "none",
              cursor: "pointer",
            }}
          >
            {floor}
          </button>
        ))}
      </div>

      <Stage width={600} height={600}>
        <Layer>
          {selectedFloor === "Parcela" && <ParcelaView />}
          {selectedFloor === "PB" && <FloorPlanPB />}
          {selectedFloor === "P1" && <FloorPlan1 />}
          {selectedFloor === "P2" && <FloorPlan2 />}
          {selectedFloor === "P3" && <FloorPlan3 />}
        </Layer>
      </Stage>
    </div>
  );
};

export default App;
